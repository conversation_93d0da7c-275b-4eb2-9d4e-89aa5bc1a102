import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'

const resources = {
  en: {
    translation: {
      title: 'Foundation',
      welcome_to_scai: 'WELCOME TO SCAI FOUNDATION',
      best_gateway: 'The Best Gateway to AI-Research & Academic',
      mission_statement: '🌐 Developing decentralized protocols and infrastructure<br/>🧠 Researching blockchain governance mechanisms and tokenomics<br/>🚀 Incubating and launching high-quality Web3 projects',
      transfer_to_emergency: 'Transfer To Emergency Account',
      notice: 'Notice',
      notice_content:
        'The Sci-Hub community migration donation has ended. All users who participated in the donation will be listed in the table below. Donors will receive a 1:1 mapping in future airdrops. In principle, the donation deadline was 0:00 UTC+8 on June 24, 2025. Users who did not participate in the donation will be snapshotted in the future. If you held Sci-Hub on-chain before 0:00 on June 21, 2025, UTC*****:00, regardless of whether you sold it afterwards, you will be snapshotted and receive proportional airdrop distribution with other snapshot users in the future. All donated funds have now entered OKX Validator and are staked in the form of SOL, and will not be moved before recreating the token. If no new token is ultimately created, these funds will be returned proportionally to the original path. If you are on the list but do not have an ETH (BSC) address, please contact the community administrator to supplement it. Community administrators will not actively DM you to request private information.',
      bsc_address: 'BSC Address',
      token_amount: 'Token Amount',
      enter_bsc_address: 'Enter your BSC address',
      enter_token_amount: 'Enter token amount',
      transfer_scihub_tokens: 'Transfer SCIHUB Tokens',
      wallet_not_connected: 'Wallet Not Connected',
      please_connect_wallet: 'Please connect your Phantom wallet.',
      invalid_bsc_address: 'Invalid BSC Address',
      please_enter_valid_bsc_address: 'Please enter a valid BSC address (starts with 0x, 42 characters).',
      invalid_token_amount: 'Invalid Token Amount',
      please_enter_minimum_tokens: 'Please enter at least {{minAmount}} SCIHUB tokens.',
      transaction_success: 'Transaction Successful',
      tokens_sent_success: 'Successfully sent {{amount}} SCIHUB tokens\nTransaction signature: {{signature}}\nBSC address: {{bscAddress}}',
      transaction_failed: 'Transaction Failed',
      donation_list: 'Donation List',
      search_placeholder: 'Please enter the SOL wallet address',
      official_website: 'OFFICIAL WEBSITE',
      app: 'APP',
      community: 'COMMUNITY',
      more_partners: 'MORE PARTNERS',
      contract_label: 'CONTRACT (Solana):',
      copy: 'Copy',
      copied: 'Copied',
      menu: 'Menu',
      fetch_failed: 'Failed to Fetch Donations',
      time: 'TIME',
      sender: 'SENDER',
      amount: 'AMOUNT',
      scihub_address: 'SciHub Address',
      eth_address: 'SOL Address',
      tx_signature: 'TRANSACTION SIGNATURE',
    },
  },
  zh: {
    translation: {
      title: '基金会',
      welcome_to_scai: '欢迎体验SCAI基金会',
      best_gateway: '人工智能研究与学术的最佳入口',
      mission_statement: '🌐 开发去中心化协议和基础设施<br/>🧠 研究区块链治理机制和代币经济学<br/>🚀 孵化和推出高质量的Web3项目',
      transfer_to_emergency: '转账至紧急账户',
      notice: '公告',
      notice_content:
        'Sci-hub社区的迁移捐赠已经结束，所有参与捐赠的用户将会公示在下方的表格当中。捐赠的用户将会在未来的空投中得到1:1的映射。原则上，捐赠时间截止到2025年6月24日UTC+8的0点。没有参与捐赠的用户，将会在未来被快照，如果您在2025年6月21日23点之前，在链上持有Sci-hub，无论您之后是否卖出，都会得到快照，并在未来和其他快照用户按比例进行空投分配。所有捐赠的资金目前已经进入OKX Validator以SOL的形式质押，在重新创建代币之前将不会移动。如果最终没有创建新代币，这些资金会按比例原路退回。如果您在名单中，但是没有eth（bsc）地址，请联系社区管理员进行补充。社区管理员不会主动DM你索要私人信息。',
      bsc_address: 'BSC地址',
      token_amount: '代币数量',
      enter_bsc_address: '请输入BSC地址',
      enter_token_amount: '请输入代币数量',
      transfer_scihub_tokens: '转移SCIHUB代币',
      wallet_not_connected: '钱包未连接',
      please_connect_wallet: '请先连接您的Phantom钱包。',
      invalid_bsc_address: '无效的BSC地址',
      please_enter_valid_bsc_address: '请输入有效的BSC地址（以0x开头，42个字符）。',
      invalid_token_amount: '无效的代币数量',
      please_enter_minimum_tokens: '请至少输入{{minAmount}}个SCIHUB代币。',
      transaction_success: '交易成功',
      tokens_sent_success: '已成功发送{{amount}}个SCIHUB代币\n交易签名: {{signature}}\nBSC地址: {{bscAddress}}',
      transaction_failed: '交易失败',
      donation_list: '捐款列表',
      search_placeholder: '请输入 SOL 钱包地址',
      official_website: '官方网站',
      app: '应用',
      community: '社区',
      more_partners: '更多合作伙伴',
      contract_label: '合约（Solana）:',
      copy: '复制',
      copied: '已复制',
      menu: '菜单',
      fetch_failed: '获取捐款失败',
      time: '时间',
      sender: '发送者',
      amount: '数量',
      eth_address: 'ETH地址',
      tx_signature: '交易签名',
    },
  },
}

i18n.use(initReactI18next).init({
  resources,
  lng: 'en',
  fallbackLng: 'en',
  interpolation: {
    escapeValue: false,
  },
})

export default i18n
