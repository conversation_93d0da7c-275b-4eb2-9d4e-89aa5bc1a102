import { NewsItem, NewsResponse, NewsApiParams } from '../types/news';

// 假数据池
const MOCK_NEWS: NewsItem[] = [
  {
    id: '1',
    title: 'Science saves lives...but the process is stuck in the past.',
    content: 'Before we make our major update, we want to keep our App with continuously new features, like a Decentralized Profile, which allows researchers to showcase their work in a decentralized manner.',
    author: '@ScienceDefi',
    publishedAt: '2024-12-28T10:30:00Z',
    category: 'news',
    tags: ['DeSci', 'Research', 'Blockchain'],
    readTime: 3,
    imageUrl: '/news.png',
  },
  {
    id: '2',
    title: 'SCAI Foundation Launches Revolutionary Research Platform',
    content: 'Our new platform integrates AI-powered research tools with blockchain technology, enabling scientists worldwide to collaborate more effectively and share their findings securely.',
    author: '@SCAI_Foundation',
    publishedAt: '2024-12-27T15:45:00Z',
    category: 'announcement',
    tags: ['AI', 'Platform', 'Collaboration'],
    readTime: 5,
    imageUrl: '/products.png',
  },
  {
    id: '3',
    title: 'Breaking: 250,000+ Research Papers Now on Blockchain',
    content: 'We have successfully uploaded over 250,000 research papers to the blockchain, making scientific knowledge more accessible and permanent than ever before.',
    author: '@SCAI_Research',
    publishedAt: '2024-12-26T09:15:00Z',
    category: 'update',
    tags: ['Milestone', 'Papers', 'Achievement'],
    readTime: 2,
    imageUrl: '/products.png',
  },
  {
    id: '4',
    title: 'New Partnership with Leading Universities Announced',
    content: 'SCAI Foundation partners with top-tier universities to accelerate scientific research through decentralized technologies and AI-powered analysis tools.',
    author: '@SCAI_Partnerships',
    publishedAt: '2024-12-25T14:20:00Z',
    category: 'news',
    tags: ['Partnership', 'Universities', 'Collaboration'],
    readTime: 4,
    imageUrl: '/cooperate.png',
  },
  {
    id: '5',
    title: 'Community Milestone: 10,000+ Active Researchers',
    content: 'Our growing community of researchers continues to expand, with over 10,000 active contributors using our platform to advance scientific discovery.',
    author: '@SCAI_Community',
    publishedAt: '2024-12-24T11:30:00Z',
    category: 'update',
    tags: ['Community', 'Growth', 'Researchers'],
    readTime: 3,
    imageUrl: '/cooperate.png',
  },
  {
    id: '6',
    title: 'AI-Powered Research Assistant Beta Launch',
    content: 'Experience the future of research with our new AI assistant that helps scientists discover relevant papers, analyze data, and generate insights faster than ever.',
    author: '@SCAI_AI',
    publishedAt: '2024-12-23T16:45:00Z',
    category: 'announcement',
    tags: ['AI', 'Beta', 'Research Assistant'],
    readTime: 6,
    imageUrl: '/products.png',
  }
];

// 模拟网络延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// 获取新闻列表
export const fetchNews = async (params: NewsApiParams = {}): Promise<NewsResponse> => {
  const { page = 1, pageSize = 10, category, search } = params;

  // 模拟网络延迟
  await delay(800 + Math.random() * 400);

  // 过滤数据
  let filteredNews = [...MOCK_NEWS];

  if (category) {
    filteredNews = filteredNews.filter(item => item.category === category);
  }

  if (search) {
    const searchLower = search.toLowerCase();
    filteredNews = filteredNews.filter(item =>
      item.title.toLowerCase().includes(searchLower) ||
      item.content.toLowerCase().includes(searchLower) ||
      item.tags.some(tag => tag.toLowerCase().includes(searchLower))
    );
  }

  // 分页
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedNews = filteredNews.slice(startIndex, endIndex);

  return {
    data: paginatedNews,
    total: filteredNews.length,
    page,
    pageSize,
    hasMore: endIndex < filteredNews.length
  };
};

// 获取单条新闻
export const fetchNewsById = async (id: string): Promise<NewsItem | null> => {
  await delay(300 + Math.random() * 200);

  const news = MOCK_NEWS.find(item => item.id === id);
  return news || null;
};

// 获取随机新闻（用于轮播）
export const fetchRandomNews = async (count: number = 3): Promise<NewsItem[]> => {
  await delay(500 + Math.random() * 300);

  const shuffled = [...MOCK_NEWS].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
};
