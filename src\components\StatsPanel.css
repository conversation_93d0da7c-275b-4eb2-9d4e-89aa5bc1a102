/* StatsPanel Responsive Styles */

/* Default Desktop Layout - 4 columns */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
}

.stats-panel-online {
  width: 20px;
  height: 20px;
}

.stats-panel-offline {
  width: 20px;
  height: 20px;
}

.stats-card{
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}

/* Mobile First Approach */
@media (max-width: 480px) {
  .stats-panel-container {
    padding: 20px 10px !important;
  }

  .stats-panel-title {
    font-size: 24px !important;
    margin-bottom: 24px !important;
  }

  .stats-grid {
    grid-template-columns: 1fr !important;
    gap: 16px !important;
  }

  .stats-card {
    min-height: auto !important;
  }

  .stats-card-title {
    font-size: 12px !important;
  }

  .stats-card-value {
    font-size: 28px !important;
  }

  .stats-card-suffix {
    font-size: 12px !important;
  }

  .countdown-value {
    font-size: 20px !important;
  }

  .countdown-labels {
    font-size: 10px !important;
  }
}

/* Tablet */
@media (min-width: 481px) and (max-width: 768px) {
  .stats-panel-container {
    padding: 30px 15px !important;
  }

  .stats-panel-title {
    font-size: 28px !important;
    margin-bottom: 32px !important;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 20px !important;
  }

  .stats-card-value {
    font-size: 32px !important;
  }

  .countdown-value {
    font-size: 24px !important;
  }
}

/* Desktop Small */
@media (min-width: 769px) and (max-width: 1024px) {
  .stats-panel-container {
    padding: 35px 20px !important;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 22px !important;
  }
}

/* Desktop Large */
@media (min-width: 1025px) {
  .stats-grid {
    grid-template-columns: repeat(4, 1fr) !important;
  }
}

/* Hover effects for desktop */
@media (hover: hover) {
  .stats-card:hover {
    transform: translateY(-4px);
    backdrop-filter: blur(35px);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.25);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .stats-card {
    border: 2px solid rgba(255, 255, 255, 0.5) !important;
  }

  .stats-card-title,
  .stats-card-value,
  .stats-card-suffix {
    text-shadow: none !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .stats-card {
    transition: none !important;
  }

  .stats-card:hover {
    transform: none !important;
  }
}
