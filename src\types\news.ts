// 新闻数据类型定义

export interface NewsItem {
  id: string;
  title: string;
  content: string;
  author: string;
  publishedAt: string;
  category: 'news' | 'update' | 'announcement' | 'research';
  tags: string[];
  readTime?: number; // 阅读时间（分钟）
  imageUrl?: string;
  url?: string; // 外部链接
}

export interface NewsResponse {
  data: NewsItem[];
  total: number;
  page: number;
  pageSize: number;
  hasMore: boolean;
}

export interface NewsApiParams {
  page?: number;
  pageSize?: number;
  category?: string;
  search?: string;
}
